package config

import "time"

// GetDataMode represents the data retrieval mode
type GetDataMode int

const (
	ModeAutoMerge GetDataMode = iota // Automatically fetch and merge data
	ModeFetchOnly                    // Only fetch to temporary storage
	ModeMergeTemp                    // Merge temporary data to production
	ModeProdOnly                     // Only use production data
)

// Default retry configuration
const (
	DefaultMaxRetries     = 3
	DefaultRetryDelay     = 500 * time.Millisecond
	DefaultExpireInterval = 7 * time.Hour * 24
)
