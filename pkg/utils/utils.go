// Package utils provides utility functions for the menukit package.
package utils

import (
	"crypto/md5"
	"fmt"
	"os"
	"path/filepath"
)

// FormatDataSize converts bytes to a more readable format
func FormatDataSize(bytes int) string {
	if bytes < 1024 {
		return fmt.Sprintf("%d B", bytes)
	} else if bytes < 1024*1024 {
		return fmt.Sprintf("%.2f KB", float64(bytes)/1024)
	} else if bytes < 1024*1024*1024 {
		return fmt.Sprintf("%.2f MB", float64(bytes)/(1024*1024))
	}
	return fmt.Sprintf("%.2f GB", float64(bytes)/(1024*1024*1024))
}

// GetFilePath joins directory and filename
func GetFilePath(dir string, fileName string) string {
	return filepath.Join(dir, fileName)
}

// GetMD5Hash returns the MD5 hash of the given data
func GetMD5Hash(data []byte) string {
	hash := md5.Sum(data)
	return fmt.Sprintf("%x", hash)
}

// DefaultSaveFunc returns a function that saves data to a file
func DefaultSaveFunc(dir string) func([]byte, string) error {
	return func(data []byte, fileName string) error {
		savePath := GetFilePath(dir, fileName)
		if savePath == "" {
			return fmt.Errorf("save path cannot be empty")
		}
		// Create directory if it doesn't exist
		if err := EnsureDir(filepath.Dir(savePath)); err != nil {
			return err
		}
		// Write file
		return os.WriteFile(savePath, data, 0644)
	}
}

// DefaultGetFunc returns a function that reads data from a file
func DefaultGetFunc(dir string) func(string) ([]byte, error) {
	return func(fileName string) ([]byte, error) {
		savePath := GetFilePath(dir, fileName)
		if _, err := os.Stat(savePath); os.IsNotExist(err) {
			return nil, nil
		}
		return os.ReadFile(savePath)
	}
}

// EnsureDir ensures that a directory exists
func EnsureDir(dir string) error {
	if dir == "" || dir == "." {
		return nil
	}
	return os.MkdirAll(dir, 0755)
}
